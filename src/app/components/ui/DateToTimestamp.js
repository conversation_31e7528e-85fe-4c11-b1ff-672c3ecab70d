import { getTranslation } from '@/lib/i18n';
import { useState } from 'react';
import { Button, Input, Select, SelectItem } from '@heroui/react';
import TimeZone from './TimeZone';
import InputWithCopy from './InputWithCopy';

export default function DateToTimestamp({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const units = [
    { key: 'Seconds', label: t('Seconds') },
    { key: 'Milliseconds', label: t('Milliseconds') },
  ];

  const [datetime, setDatetime] = useState('');
  const [timezone, setTimezone] = useState('');
  const [unit, setUnit] = useState('Seconds');
  const [convertedTimestamp, setConvertedTimestamp] = useState('');

  const convertTimestamp = () => { };

  return (
    <div className="section mt-10 border rounded-lg px-6 py-4">
      <h2 className="text-2xl font-bold mb-4">{t('Date to Timestamp')}</h2>
      <div className='flex gap-2 items-center flex-wrap'>
        <Input
          type="text"
          placeholder={t('YYYY-MM-DD hh:mm:ss')}
          value={datetime}
          onValueChange={setDatetime}
          className="w-[160px]"
          label={t('Date')}
        />
        <TimeZone
          locale={locale}
          timezone={timezone}
          onChange={setTimezone}
        ></TimeZone>
        <Select
          selectedKeys={[unit]}
          onSelectionChange={(keys) => setUnit(Array.from(keys)[0])}
          className="w-[140px]"
          label={t('Unit')}
        >
          {units.map((unitOption) => (
            <SelectItem key={unitOption.key} value={unitOption.key}>
              {unitOption.label}
            </SelectItem>
          ))}
        </Select>
        <Button onPress={convertTimestamp} color="primary">
          {t('Convert')}
        </Button>
      </div>
      <InputWithCopy locale={locale} text={convertedTimestamp} class="mt-2"></InputWithCopy>
    </div>
  );
}

import { Button } from '@heroui/react';
import { getTranslation } from '@/lib/i18n';
import { clsx } from 'clsx';

export default function InputWithCopy({ locale = 'en', text = '', className = '' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  return (
    <div className={clsx("relative w-full md:w-[320px]", className)}>
      <input
        type="text"
        className="w-full px-4 py-3 pr-24 rounded-xl border border-foreground/10"
        placeholder={t('convert result')}
        value={text}
      />
      <Button
        className="absolute right-2 top-1/2 -translate-y-1/2 px-3 py-1 rounded-lg"
        onPress={() => copyToClipboard(text)}>
        {t('Copy')}
      </Button>
    </div>
  );
}

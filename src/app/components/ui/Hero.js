'use client';
import { getTranslation } from '@/lib/i18n';
import CurrentTimestamp from './CurrentTimestamp';
import TimestampToDate from './TimestampToDate';
import DateToTimestamp from './DateToTimestamp';

export default function Hero({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <div className="text-center pt-10 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {t('Unix Timestamp Converter')}
      </h1>
      <div className="section text-left mt-8 md:px-40">
        <CurrentTimestamp locale={locale} />
        <TimestampToDate locale={locale} />
        <DateToTimestamp locale={locale} />
      </div>
    </div>
  );
}
